name: Process Pending Payouts - Staging

on:
  schedule:
    - cron: "*/15 * * * *" # Runs every 15 minutes
  workflow_dispatch: # Allows manual triggering

jobs:
  process-payouts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20.12.0"
          cache: "pnpm"
      - uses: pnpm/action-setup@v2
        with:
          version: 9
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Process pending payouts
        run: cd packages/server && pnpm run cron:process-payouts
        env:
          DB_URL: ${{ secrets.DB_URL_STAGING }}
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY_STAGING }}
