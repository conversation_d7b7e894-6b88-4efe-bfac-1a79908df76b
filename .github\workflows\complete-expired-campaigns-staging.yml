name: Complete Expired Campaigns - Staging

on:
  schedule:
    - cron: "0 0 * * *" # Runs at midnight UTC every day
  workflow_dispatch: # Allows manual triggering

jobs:
  complete-campaigns:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20.12.0"
          cache: "pnpm"
      - uses: pnpm/action-setup@v2
        with:
          version: 9
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Run cron job
        run: cd packages/server && pnpm run cron:complete-campaigns
        env:
          DB_URL: ${{ secrets.DB_URL_STAGING }}
